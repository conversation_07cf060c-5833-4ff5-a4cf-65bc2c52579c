// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyA3mIlC6O1_tGeH7OpuZmEcDYSDPcNcMqo',
    appId: '1:1068175978703:web:a62aa1bf9668a7b9dcc7ba',
    messagingSenderId: '1068175978703',
    projectId: 'bloomg-flutter',
    authDomain: 'bloomg-flutter.firebaseapp.com',
    storageBucket: 'bloomg-flutter.firebasestorage.app',
    measurementId: 'G-17QGQMB6GZ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB8dX4BB4GvYkO59QIg0FU6XcwqvGc9VOE',
    appId: '1:1068175978703:android:a4ca0919871b7c57dcc7ba',
    messagingSenderId: '1068175978703',
    projectId: 'bloomg-flutter',
    storageBucket: 'bloomg-flutter.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyB6UHzwmmH1yAMRJ042Dw9HyOA3S3o1830',
    appId: '1:1068175978703:ios:7409e00f1aad1ccddcc7ba',
    messagingSenderId: '1068175978703',
    projectId: 'bloomg-flutter',
    storageBucket: 'bloomg-flutter.firebasestorage.app',
    androidClientId: '1068175978703-evvnm6i3lrohe98pe32j86p41drijr1m.apps.googleusercontent.com',
    iosClientId: '1068175978703-lvnoe7hq6kau37vmucoqu2c4c140ajlh.apps.googleusercontent.com',
    iosBundleId: 'com.algomash.radiance',
  );

}